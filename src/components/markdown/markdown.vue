<script setup>
// import UaMarkdown from './ua-markdown/ua-markdown.vue'
import MpHtml from './mp-html/mp-html.vue'

defineProps({
  // 解析内容
  message: String,
})
</script>

<template>
  <!-- <UaMarkdown :source="message" :autoWrap="true" :showLine="true" /> -->
  <MpHtml
    :content="message" :markdown="true" :selectable="true" :scroll-table="true" :show-img-menu="true"
    container-style="font-size:14px;white-space: pre-wrap; word-wrap: break-word; max-width: 100%;"
  />
</template>
