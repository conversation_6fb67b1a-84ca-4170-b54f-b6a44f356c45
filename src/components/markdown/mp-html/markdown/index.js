/**
 * @fileoverview Markdown解析插件（带缓冲区版本）
 * 新增特性：
 * 1. 分块缓冲区处理机制（提升大文本性能）
 * 2. LRU缓存系统
 * 3. 流式处理支持
 */

import marked from './marked.min.js'

const BUFFER_SIZE = 8192 // 4KB分块
const CACHE_SIZE = 100 // LRU缓存容量

function Markdown(vm) {
  this.vm = vm
  vm._ids = {}
  this.latexBlocks = []
  this.bufferCache = new Map()
  this.lruKeys = []

  // 初始化marked
  marked.setOptions({
    breaks: true,
    smartLists: true,
  })
}

// ================== 新增缓冲区方法 ==================
Markdown.prototype.chunkProcessor = function (content) {
  const chunks = []
  let pos = 0

  // 分块处理循环
  while (pos < content.length) {
    let end = pos + BUFFER_SIZE
    // 确保不拆分LaTeX表达式
    const nextDollar = content.indexOf('$$', end)
    if (nextDollar !== -1 && nextDollar < end + 20) {
      end = nextDollar + 2
    }
    chunks.push(content.slice(pos, end))
    pos = end
  }

  return chunks
}

marked.use({
  extensions: [{
    name: 'think',
    level: 'block',
    start(src) {
      // 检测到 `<think>` 标签时触发解析
      return src.match(/<think>/)?.index
    },
    tokenizer(src) {
      // 匹配 <think>...</think> 及其内容
      const rule = /^<think>([\s\S]*?)<\/think>/
      const match = rule.exec(src)
      if (match) {
        return {
          type: 'think',
          raw: match[0],
          text: match[1].trim(), // 提取标签内的内容
        }
      }
    },
    renderer(token) {
      // 将内容渲染为 blockquote（根据 [[1]] 的需求）

      return `<div class="think ">${token.text}</div>`
    },
  }],
})

Markdown.prototype.processChunk = function (chunk) {
  // 缓存检查
  const hash = this.simpleHash(chunk)
  if (this.bufferCache.has(hash)) {
    return this.bufferCache.get(hash)
  }

  // 处理逻辑
  let protectedChunk = this.protectLatex(chunk)
  let html = marked(protectedChunk)
  html = this.restoreLatex(html)

  // 更新缓存
  if (this.bufferCache.size >= CACHE_SIZE) {
    const oldKey = this.lruKeys.shift()
    this.bufferCache.delete(oldKey)
  }
  this.bufferCache.set(hash, html)
  this.lruKeys.push(hash)

  return html
}

Markdown.prototype.simpleHash = function (str) {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    hash = ((hash << 5) - hash) + str.charCodeAt(i)
    hash |= 0
  }
  return hash
}

// ================== 优化的Latex处理 ==================
Markdown.prototype.protectLatex = function (content) {
  this.latexBlocks = []
  return content.replace(
    /(?<![`$>\-])\${1,2}(?![`$])((?:\\.|[^\\$])+)(?<![`$])\${1,2}(?![`$<\d])/g,
    (match) => {
      this.latexBlocks.push(match)
      return `@@LATEX${this.latexBlocks.length - 1}@@`
    },
  )
}

Markdown.prototype.restoreLatex = function (html) {
  return html.replace(
    /@@LATEX(\d+)@@/g,
    (_, index) => this.latexBlocks[Number(index)] || '',
  )
}

// ================== 更新主处理流程 ==================
Markdown.prototype.onUpdate = function (content) {
  if (!this.vm.markdown) { return '' }

  // 分块处理
  const chunks = this.chunkProcessor(content)
  let finalHTML = ''

  for (const chunk of chunks) {
    const processed = this.processChunk(chunk)
    finalHTML += processed
  }

  // 后处理校准
  return this.postProcess(finalHTML)
}

Markdown.prototype.postProcess = function (html) {
  return html
    .replace(/<\/?buffer>/g, '') // 清理可能的残留标记
    .replace(/(@@LATEX\d+@@){2,}/g, (m) => { // 清理未匹配的占位符
      const lastIndex = Number.parseInt(m.match(/(\d+)/)[0], 10)
      return this.latexBlocks[lastIndex] || ''
    })
}

Markdown.prototype.onParse = function (node, vm) {
  if (vm.options.markdown) {
    // 中文 id 处理
    if (vm.options.useAnchor && node.attrs && /[\u4E00-\u9FA5]/.test(node.attrs.id)) {
      const id = `t${index++}`
      this.vm._ids[node.attrs.id] = id
      node.attrs.id = id
    }
    // 简化类名处理
    // const classNameMap = {
    //   p: 'md-paragraph',
    //   code: 'inline-code',
    //   pre: 'code-block'
    // }
    // if (classNameMap[node.name]) {
    //   node.attrs.class = `${classNameMap[node.name]} ${node.attrs.class || ''}`
    // }
    if (node.name === 'p' || node.name === 'table' || node.name === 'tr' || node.name === 'th' || node.name === 'td' || node.name === 'blockquote' || node.name === 'pre' || node.name === 'code') {
      node.attrs.class = `md-${node.name} ${node.attrs.class || ''}`
    }
  }
}

export default Markdown
